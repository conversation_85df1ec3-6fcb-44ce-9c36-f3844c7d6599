<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Ink Request Form View -->
        <record id="view_ink_request_form" model="ir.ui.view">
            <field name="name">bssic.ink.request.form</field>
            <field name="model">bssic.ink.request</field>
            <field name="arch" type="xml">
                <form string="Ink Request">
                    <header>
                        <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                        <button name="action_approve_direct_manager" string="Approve (Manager)" type="object" class="oe_highlight" states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                        <button name="action_approve_warehouse" string="Approve (Warehouse)" type="object" class="oe_highlight" states="warehouse_approval" groups="bssic_requests.group_bssic_warehouse_manager"/>
                        <button name="action_approve_hr" string="Approve (HR)" type="object" class="oe_highlight" states="hr_approval" groups="bssic_requests.group_bssic_hr_manager"/>
                        <button name="action_confirm_receipt_wizard" string="Confirm Receipt" type="object" class="oe_highlight" states="pending_receipt"/>
                        <button name="action_reject" string="Reject" type="object" class="btn-danger" states="direct_manager,warehouse_approval,hr_approval" groups="bssic_requests.group_bssic_direct_manager,bssic_requests.group_bssic_warehouse_manager,bssic_requests.group_bssic_hr_manager"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,direct_manager,warehouse_approval,hr_approval,pending_receipt,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="employee_number" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="employee_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="department_id" readonly="1"/>
                                <field name="job_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="direct_manager_id" readonly="1"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Printer Ink" name="printer_ink">
                                <field name="printer_ink_line_ids" attrs="{'readonly': [('state', '!=', 'draft')]}">
                                    <tree editable="bottom">
                                        <field name="printer_number" string="رقم الطابعة"/>
                                        <field name="quantity" string="الكمية"/>
                                        <field name="ink_number" string="رقم الحبر"/>
                                        <field name="stock_quantity" attrs="{'readonly': [('can_edit_stock_quantity', '=', False)]}" optional="hide"/>
                                        <field name="approved_quantity" attrs="{'readonly': [('can_edit_approved_quantity', '=', False)]}" optional="hide"/>
                                        <field name="notes"/>
                                        <field name="can_edit_stock_quantity" invisible="1"/>
                                        <field name="can_edit_approved_quantity" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Copier Ink" name="copier_ink">
                                <field name="copier_ink_line_ids" attrs="{'readonly': [('state', '!=', 'draft')]}">
                                    <tree editable="bottom">
                                        <field name="copier_number" string="رقم آلة التصوير"/>
                                        <field name="quantity" string="الكمية"/>
                                        <field name="ink_number" string="رقم الحبر"/>
                                        <field name="stock_quantity" attrs="{'readonly': [('can_edit_stock_quantity', '=', False)]}" optional="hide"/>
                                        <field name="approved_quantity" attrs="{'readonly': [('can_edit_approved_quantity', '=', False)]}" optional="hide"/>
                                        <field name="notes"/>
                                        <field name="can_edit_stock_quantity" invisible="1"/>
                                        <field name="can_edit_approved_quantity" invisible="1"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="Activity Log" name="activity_log">
                                <field name="activity_log_ids" readonly="1">
                                    <tree>
                                        <field name="activity_date"/>
                                        <field name="activity_type"/>
                                        <field name="user_id"/>
                                        <field name="employee_id"/>
                                        <field name="activity_description"/>
                                        <field name="notes"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="Notes" name="notes">
                                <group>
                                    <field name="notes" nolabel="1"/>
                                </group>
                                <group string="Rejection Details" attrs="{'invisible': [('state', '!=', 'rejected')]}">
                                    <field name="rejection_reason" nolabel="1"/>
                                </group>
                                <group string="Receipt Details" attrs="{'invisible': [('state', 'not in', ['pending_receipt', 'completed'])]}">
                                    <field name="receipt_notes" nolabel="1" attrs="{'readonly': [('state', '=', 'completed')]}"/>
                                </group>
                            </page>
                        </notebook>

                        <!-- Approval Information -->
                        <group string="Approval Information" attrs="{'invisible': [('state', 'in', ['draft', 'submitted'])]}">
                            <group>
                                <field name="submission_user_id" readonly="1"/>
                                <field name="submission_date" readonly="1"/>
                                <field name="direct_manager_approval_user_id" readonly="1" attrs="{'invisible': [('direct_manager_approval_user_id', '=', False)]}"/>
                                <field name="direct_manager_approval_date" readonly="1" attrs="{'invisible': [('direct_manager_approval_date', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="warehouse_approval_user_id" readonly="1" attrs="{'invisible': [('warehouse_approval_user_id', '=', False)]}"/>
                                <field name="warehouse_approval_date" readonly="1" attrs="{'invisible': [('warehouse_approval_date', '=', False)]}"/>
                                <field name="hr_approval_user_id" readonly="1" attrs="{'invisible': [('hr_approval_user_id', '=', False)]}"/>
                                <field name="hr_approval_date" readonly="1" attrs="{'invisible': [('hr_approval_date', '=', False)]}"/>
                            </group>
                        </group>

                        <!-- Hidden fields for computation -->
                        <field name="is_manager" invisible="1"/>
                        <field name="is_hr_manager" invisible="1"/>
                        <field name="is_warehouse_manager" invisible="1"/>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Ink Request Tree View -->
        <record id="view_ink_request_tree" model="ir.ui.view">
            <field name="name">bssic.ink.request.tree</field>
            <field name="model">bssic.ink.request</field>
            <field name="arch" type="xml">
                <tree string="Ink Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="request_date"/>
                    <field name="state" decoration-info="state=='draft'"
                           decoration-warning="state in ['submitted','direct_manager','warehouse_approval','hr_approval']"
                           decoration-success="state=='completed'"
                           decoration-danger="state=='rejected'"/>
                </tree>
            </field>
        </record>

        <!-- Ink Request Search View -->
        <record id="view_ink_request_search" model="ir.ui.view">
            <field name="name">bssic.ink.request.search</field>
            <field name="model">bssic.ink.request</field>
            <field name="arch" type="xml">
                <search string="Ink Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <separator/>
                    <filter string="My Requests" name="my_requests"
                            domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Pending Approval" name="pending"
                            domain="[('state', 'in', ['submitted', 'direct_manager', 'warehouse_approval', 'hr_approval'])]"/>
                    <filter string="Pending Receipt" name="pending_receipt" domain="[('state', '=', 'pending_receipt')]"/>
                    <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                    <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                        <filter string="Department" name="group_department" context="{'group_by': 'department_id'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Request Date" name="group_date" context="{'group_by': 'request_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Ink Request Action for Employees (Own Requests Only) -->
        <record id="action_ink_request_employee" model="ir.actions.act_window">
            <field name="name">Ink Requests</field>
            <field name="res_model">bssic.ink.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('employee_id.user_id', '=', uid)]</field>
            <field name="context">{}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_ink_request_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_ink_request_form')})]"/>
            <field name="groups_id" eval="[(4, ref('bssic_requests.group_bssic_employee'))]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Ink Request!
                </p>
                <p>
                    Submit requests for printer and copier ink supplies.
                </p>
            </field>
        </record>

        <!-- Ink Request Action for Managers (All Requests) -->
        <record id="action_ink_request_manager" model="ir.actions.act_window">
            <field name="name">Ink Requests</field>
            <field name="res_model">bssic.ink.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{}</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_ink_request_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_ink_request_form')})]"/>
            <field name="groups_id" eval="[
                (4, ref('bssic_requests.group_bssic_direct_manager')),
                (4, ref('bssic_requests.group_bssic_warehouse_manager')),
                (4, ref('bssic_requests.group_bssic_hr_manager'))
            ]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Manage Ink Requests!
                </p>
                <p>
                    Review and approve ink requests from employees.
                </p>
            </field>
        </record>

        <!-- Menu Item for Ink Requests (Employees) -->
        <menuitem id="menu_ink_request_employee"
                  name="طلب حبر"
                  parent="menu_bssic_stationery"
                  action="action_ink_request_employee"
                  groups="bssic_requests.group_bssic_employee"
                  sequence="20"/>

        <!-- Menu Item for Ink Requests (Managers) -->
        <menuitem id="menu_ink_request_manager"
                  name="طلب حبر (الكل)"
                  parent="menu_bssic_stationery"
                  action="action_ink_request_manager"
                  groups="bssic_requests.group_bssic_direct_manager,bssic_requests.group_bssic_warehouse_manager,bssic_requests.group_bssic_hr_manager"
                  sequence="21"/>

    </data>
</odoo>
